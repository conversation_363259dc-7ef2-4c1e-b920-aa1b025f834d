{"name": "s<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.2", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/masked-view": "^0.1.11", "@react-native-google-signin/google-signin": "^14.0.1", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/drawer": "^7.4.1", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.13", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "expo": "^53.0.12", "expo-asset": "^11.1.5", "expo-auth-session": "~6.2.0", "expo-av": "~15.1.6", "expo-constants": "~17.1.6", "expo-contacts": "~14.2.5", "expo-device": "~7.1.4", "expo-file-system": "~18.1.10", "expo-firebase-analytics": "^8.0.0", "expo-font": "~13.3.1", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-notifications": "~0.31.3", "expo-random": "^14.0.1", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "lottie-react-native": "^7.2.2", "react": "19.0.0", "react-native": "^0.79.4", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "^4.0.3", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "expo-screen-orientation": "~8.1.7"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}