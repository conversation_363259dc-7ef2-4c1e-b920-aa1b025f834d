# Payment Testing Guide - WebView Implementation

## 🎯 Quick Start Testing

### 1. **Start the Development Server**
```bash
cd shash_native
npm start
```

### 2. **Open in Expo Go**
- Scan the QR code with Expo Go app (Android) or Camera app (iOS)
- Or press 'w' to open in web browser for initial testing

### 3. **Navigate to Payment Screens**
- Go to Packages → Select a package → Checkout
- Or add items to cart → Go to Cart → Checkout

## 🧪 Test Scenarios

### ✅ **Scenario 1: Successful Package Purchase**
1. Navigate to Packages screen
2. Select any package
3. Click "Buy Now" or "Subscribe"
4. Fill in coupon/gift card (optional)
5. Click "Proceed to Pay"
6. **Expected**: WebView modal opens with Razorpay checkout
7. Complete payment with test credentials
8. **Expected**: Success screen appears

### ✅ **Scenario 2: Cart Purchase**
1. Add multiple items to cart
2. Go to Cart screen
3. Click "Checkout"
4. Apply discounts (optional)
5. Click "Pay Now"
6. **Expected**: WebView modal opens
7. Complete payment
8. **Expected**: Cart clears and success screen shows

### ✅ **Scenario 3: Payment Cancellation**
1. Start any payment flow
2. When WebView opens, close it using the X button
3. **Expected**: Returns to previous screen without error

### ✅ **Scenario 4: Payment Failure**
1. Start payment flow
2. Use invalid payment details in Razorpay
3. **Expected**: Failure screen appears with retry option

### ✅ **Scenario 5: Dark Mode Testing**
1. Toggle dark mode in app settings
2. Start payment flow
3. **Expected**: WebView modal respects dark theme

## 🔧 Debugging Steps

### If Payment Modal Doesn't Open
1. Check console logs for errors
2. Verify internet connection
3. Check if `EXPO_PUBLIC_BASE_URL` is set correctly
4. Ensure Razorpay configuration is valid

### If Payment Doesn't Start
1. Open browser developer tools in WebView
2. Check for JavaScript errors
3. Verify Razorpay script loading
4. Check API responses

### If Payment Verification Fails
1. Check server logs
2. Verify payment verification endpoint
3. Check authentication tokens
4. Validate payment signature

## 🛠 Test Data

### Test Razorpay Credentials
```
Card Number: 4111 1111 1111 1111
Expiry: Any future date
CVV: Any 3 digits
Name: Any name
```

### Test UPI
```
UPI ID: success@razorpay
```

### Test Netbanking
```
Bank: Any test bank
Credentials: Use test credentials provided by Razorpay
```

## 📱 Device Testing

### Android Testing
1. Install Expo Go from Play Store
2. Scan QR code from terminal
3. Test payment flow
4. Check WebView performance

### iOS Testing
1. Install Expo Go from App Store
2. Scan QR code with Camera app
3. Test payment flow
4. Verify modal presentation

### Web Testing
1. Press 'w' in terminal
2. Open in browser
3. Test basic functionality
4. Note: Some mobile-specific features may not work

## 🔍 What to Look For

### ✅ **Success Indicators**
- WebView modal opens smoothly
- Razorpay checkout loads completely
- Payment processing works
- Success/failure navigation works
- No console errors
- Dark mode works correctly

### ❌ **Failure Indicators**
- Modal doesn't open
- WebView shows blank screen
- JavaScript errors in console
- Payment doesn't process
- App crashes
- Navigation issues

## 📊 Performance Testing

### Load Time Testing
1. Measure time from "Pay Now" click to WebView display
2. **Target**: < 3 seconds
3. Check on different network speeds

### Memory Testing
1. Monitor app memory usage during payment
2. Check for memory leaks after payment completion
3. Test multiple payment attempts

### Battery Testing
1. Monitor battery usage during payment
2. Ensure WebView doesn't drain battery excessively

## 🚨 Error Scenarios to Test

### Network Issues
1. Start payment with good connection
2. Disconnect internet during payment
3. **Expected**: Graceful error handling

### App Backgrounding
1. Start payment flow
2. Background the app
3. Return to app
4. **Expected**: Payment state preserved or graceful recovery

### Low Memory
1. Open multiple apps
2. Start payment with low available memory
3. **Expected**: Payment completes or fails gracefully

## 📝 Test Checklist

### Pre-Testing Setup
- [ ] Development server running
- [ ] Environment variables configured
- [ ] Test device/emulator ready
- [ ] Internet connection stable
- [ ] Razorpay test mode enabled

### Core Functionality
- [ ] Package purchase flow works
- [ ] Cart purchase flow works
- [ ] Payment modal opens correctly
- [ ] Razorpay checkout loads
- [ ] Payment processing works
- [ ] Success navigation works
- [ ] Failure handling works
- [ ] Payment cancellation works

### UI/UX Testing
- [ ] Modal presentation smooth
- [ ] Loading states work
- [ ] Dark mode support
- [ ] Close button works
- [ ] Responsive design
- [ ] Error messages clear

### Edge Cases
- [ ] Network disconnection
- [ ] App backgrounding
- [ ] Low memory scenarios
- [ ] Multiple rapid payments
- [ ] Invalid payment data

## 🎯 Success Criteria

### Must Have
✅ Payment initialization works (no more "failed" errors)
✅ WebView modal opens and displays Razorpay
✅ Payment completion triggers success flow
✅ Payment failure triggers failure flow
✅ App doesn't crash during payment

### Nice to Have
✅ Fast loading times (< 3 seconds)
✅ Smooth animations
✅ Perfect dark mode support
✅ Excellent error messages
✅ Optimal performance

## 🔄 Continuous Testing

### After Each Change
1. Test basic payment flow
2. Check console for errors
3. Verify no regressions

### Before Release
1. Full test suite execution
2. Multiple device testing
3. Performance validation
4. Security review

## 📞 Getting Help

### Common Issues
1. **WebView blank**: Check internet and API endpoints
2. **Payment not starting**: Verify Razorpay configuration
3. **Modal not closing**: Check message handling
4. **App crashes**: Check memory usage and logs

### Debug Commands
```bash
# View logs
npx expo logs

# Clear cache
npx expo r -c

# Check bundle
npx expo export

# Debug build
npx expo run:android --variant debug
```

Remember: The goal is to ensure the payment flow works seamlessly without the "payment initialization failed" error that was occurring with the native Razorpay SDK.
