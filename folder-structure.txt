shash_native/
├── app.config.js               # Expo app configuration (single source of truth)
├── App.js

├── eas.json
├── index.js
├── package.json
├── README.md
├── assets/
│   ├── adaptive-icon.png
│   ├── favicon.png
│   ├── icon.png
│   └── splash-icon.png
├── src/
│   ├── assets/
│   │   └── placeholder.png
│   ├── components/
│   │   ├── Button.js
│   │   ├── Card.js
│   │   ├── HeaderMenu.js
│   │   ├── LoadingSpinner.js
│   │   ├── SaveButton.js
│   │   ├── ShareButton.js
│   │   └── SkeletonLoader.js
│   ├── config/
│   │   └── auth.js
│   ├── context/
│   │   └── ThemeContext.js
│   ├── navigation/
│   │   ├── AppNavigator.js
│   │   ├── AuthNavigator.js
│   │   └── TabNavigator.js
│   ├── redux/
│   │   ├── authSlice.js
│   │   ├── postsSlice.js
│   │   └── store.js
│   ├── screens/
│   │   ├── BlogPostScreen.js
│   │   ├── HomeScreen.js
│   │   ├── NewsScreen.js
│   │   ├── SavedScreen.js
│   │   └── auth/
│   │       ├── ChooseAuthScreen.js
│   │       ├── ForgotPasswordScreen.js
│   │       ├── LoginScreen.js
│   │       ├── ProfileScreen.js
│   │       └── SignupScreen.js
│   └── utils/
│       └── helpers.js
