import React, { useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { FontAwesome } from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';
import { updateQuantity, removeFromCart } from '../redux/cartSlice';
import QuantitySelector from './QuantitySelector';

const CartItem = ({ item, onPress }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const dispatch = useDispatch();

  const handleQuantityIncrease = () => {
    dispatch(updateQuantity({
      id: item.id,
      type: item.type,
      quantity: item.quantity + 1
    }));
  };

  const handleQuantityDecrease = () => {
    if (item.quantity > 1) {
      dispatch(updateQuantity({
        id: item.id,
        type: item.type,
        quantity: item.quantity - 1
      }));
    } else {
      handleRemove();
    }
  };

  const handleRemove = () => {
    Alert.alert(
      'Remove Item',
      `Are you sure you want to remove "${item.name}" from your cart?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            dispatch(removeFromCart({
              id: item.id,
              type: item.type
            }));
          },
        },
      ]
    );
  };

  const formatPrice = (price) => {
    return `₹${price.toLocaleString('en-IN')}`;
  };

  const totalPrice = item.price * item.quantity;

  return (
    <TouchableOpacity
      style={[
        styles.container,
        isDarkMode && styles.containerDark
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        {/* Item Image */}
        <View style={styles.imageContainer}>
          {item.image ? (
            <Image
              source={{ uri: item.image }}
              style={styles.image}
              resizeMode="cover"
            />
          ) : (
            <View style={[
              styles.placeholderImage,
              isDarkMode && styles.placeholderImageDark
            ]}>
              <Icon
                name={item.type === 'package' ? 'cube' : 'shopping-bag'}
                size={24}
                color={isDarkMode ? '#666' : '#999'}
              />
            </View>
          )}
        </View>

        {/* Item Details */}
        <View style={styles.details}>
          <Text style={[
            styles.name,
            isDarkMode && styles.textDark
          ]} numberOfLines={2}>
            {item.name}
          </Text>

          {item.description && (
            <Text style={[
              styles.description,
              isDarkMode && styles.descriptionDark
            ]} numberOfLines={2}>
              {item.description}
            </Text>
          )}

          <View style={styles.priceRow}>
            <Text style={[
              styles.price,
              isDarkMode && styles.textDark
            ]}>
              {formatPrice(item.price)}
            </Text>
            {item.quantity > 1 && (
              <Text style={[
                styles.totalPrice,
                isDarkMode && styles.totalPriceDark
              ]}>
                Total: {formatPrice(totalPrice)}
              </Text>
            )}
          </View>
        </View>

        {/* Quantity Controls */}
        <View style={styles.controls}>
          <TouchableOpacity
            style={[
              styles.removeButton,
              isDarkMode && styles.removeButtonDark
            ]}
            onPress={handleRemove}
            activeOpacity={0.7}
          >
            <Icon
              name="trash"
              size={16}
              color={isDarkMode ? '#ff6b6b' : '#dc3545'}
            />
          </TouchableOpacity>

          <QuantitySelector
            quantity={item.quantity}
            onIncrease={handleQuantityIncrease}
            onDecrease={handleQuantityDecrease}
            maxQuantity={item.maxQuantity || 99}
            size="small"
          />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginVertical: 6,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  containerDark: {
    backgroundColor: '#1e1e1e',
    shadowColor: '#000',
  },
  content: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  imageContainer: {
    marginRight: 12,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  placeholderImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderImageDark: {
    backgroundColor: '#333',
  },
  details: {
    flex: 1,
    marginRight: 12,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  descriptionDark: {
    color: '#999',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  price: {
    fontSize: 16,
    fontWeight: '700',
    color: '#198754',
  },
  totalPrice: {
    fontSize: 14,
    fontWeight: '600',
    color: '#198754',
  },
  totalPriceDark: {
    color: '#4CAF50',
  },
  textDark: {
    color: '#fff',
  },
  controls: {
    alignItems: 'center',
  },
  removeButton: {
    padding: 8,
    marginBottom: 8,
    borderRadius: 6,
    backgroundColor: '#f8f9fa',
  },
  removeButtonDark: {
    backgroundColor: '#333',
  },
});

export default CartItem;
