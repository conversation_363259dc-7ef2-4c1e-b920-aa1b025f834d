import React, { useState, useCallback, useContext } from "react";
import { View, TouchableOpacity, Modal, Text, StyleSheet } from "react-native";
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useDispatch, useSelector } from "react-redux";
import { useNavigation, useRoute } from "@react-navigation/native";
import { logout, getStudentProfile } from "../redux/authSlice";
import { ThemeContext } from "../context/ThemeContext";
import CartIcon from "./CartIcon";

export default function HeaderMenu({ onToggleDarkMode, isDarkMode }) {
  const [menuVisible, setMenuVisible] = useState(false);
  const [themeMenuVisible, setThemeMenuVisible] = useState(false);
  const { themeMode, setSystemTheme, setLightTheme, setDarkTheme } = useContext(ThemeContext);
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();

  const handleLogout = useCallback(() => {
    dispatch(logout());
    setMenuVisible(false);
  }, [dispatch]);

  const handleLogin = useCallback(() => {
    navigation.navigate("Login");
    setMenuVisible(false);
  }, [navigation]);

  const handleSignup = useCallback(() => {
    navigation.navigate("Signup");
    setMenuVisible(false);
  }, [navigation]);
  const handleProfile = useCallback(() => {
    setMenuVisible(false);

    // user object comes from the login response:
    // { user: {...}, id: ..., student_id: ... }
    const studentId = user?.id;
    console.log("Fetching user:", user);
    console.log("Fetching profile for student ID:", studentId);
    if (studentId) {
      dispatch(getStudentProfile({ id: studentId }))
        .unwrap()
        .then(() => {
          // Navigate through the proper navigation hierarchy
          const parent = navigation.getParent();
          if (parent) {
            parent.navigate('MainTabs', {
              screen: 'HomeTab',
              params: {
                screen: 'Profile'
              }
            });
          } else {
            navigation.navigate("Profile");
          }
        })
        .catch((err) => {
          console.warn("Failed to load profile:", err);
        });
    } else {
      console.warn("No student info available to fetch profile.");
    }
  }, [dispatch, navigation, user]);

  const handleToggleDarkMode = useCallback(() => {
    onToggleDarkMode();
    setMenuVisible(false);
  }, [onToggleDarkMode]);

  const handleThemeMenuToggle = useCallback(() => {
    setThemeMenuVisible(!themeMenuVisible);
  }, [themeMenuVisible]);

  const handleThemeSelection = useCallback((themeType) => {
    switch (themeType) {
      case 'system':
        setSystemTheme();
        break;
      case 'light':
        setLightTheme();
        break;
      case 'dark':
        setDarkTheme();
        break;
    }
    setThemeMenuVisible(false);
    setMenuVisible(false);
  }, [setSystemTheme, setLightTheme, setDarkTheme]);

  const getThemeIcon = (theme) => {
    switch (theme) {
      case 'system':
        return 'phone-portrait-outline';
      case 'light':
        return 'sunny-outline';
      case 'dark':
        return 'moon-outline';
      default:
        return 'phone-portrait-outline';
    }
  };

  const getThemeLabel = (theme) => {
    switch (theme) {
      case 'system':
        return 'System';
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      default:
        return 'System';
    }
  };

  const handleCartPress = useCallback(() => {
    // Get the current navigation state to determine where we are
    const state = navigation.getState();
    const currentRoute = state.routes[state.index];

    // Check if we're in a drawer screen
    if (currentRoute?.name && currentRoute.name !== 'MainTabs') {
      // We're in a drawer screen, navigate to Cart in drawer first
      navigation.navigate('Cart');
    } else {
      // We're in main tabs, try to navigate within the current tab
      try {
        navigation.navigate('CartScreen');
      } catch (error) {
        // Fallback to navigating through MainTabs
        try {
          navigation.navigate('MainTabs', {
            screen: 'HomeTab',
            params: { screen: 'CartScreen' }
          });
        } catch (error2) {
          // Final fallback to drawer
          navigation.navigate('Cart');
        }
      }
    }
  }, [navigation]);

  // Helper to determine if current screen is Home
  const isHomeScreen = route?.name === 'Home' || route?.name === 'HomeTab';

  return (
    <View style={styles.headerContainer}>
      {/* Cart Icon and Menu */}
      <CartIcon
        onPress={handleCartPress}
        size={24}
        color={isDarkMode ? "#fff" : "#000"}
        style={styles.cartIcon}
      />
      <TouchableOpacity onPress={() => setMenuVisible(true)}>
        <Ionicons
          name="ellipsis-vertical"
          size={24}
          color={isDarkMode ? "#fff" : "#000"}
        />
      </TouchableOpacity>

      <Modal
        animationType="fade"
        transparent={true}
        visible={menuVisible}
        onRequestClose={() => setMenuVisible(false)}>
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setMenuVisible(false)}>
          <View
            style={[
              styles.menuContainer,
              isDarkMode && styles.menuContainerDark,
            ]}>
            {isAuthenticated ? (
              <>
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={handleProfile}>
                  <Ionicons
                    name="person-outline"
                    size={20}
                    color={isDarkMode ? "#fff" : "#000"}
                  />
                  <Text
                    style={[
                      styles.menuText,
                      isDarkMode && styles.menuTextDark,
                    ]}>
                    Profile
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.menuItem}                  onPress={() => {
                    setMenuVisible(false);
                    navigation.navigate("Packages");
                  }}>
                  <Ionicons
                    name="cube-outline"
                    size={20}
                    color={isDarkMode ? "#fff" : "#000"}
                  />
                  <Text
                    style={[
                      styles.menuText,
                      isDarkMode && styles.menuTextDark,
                    ]}>
                    Packages
                  </Text>
                </TouchableOpacity>

                {/* Refer & Earn */}

                {/* <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setMenuVisible(false);
                    navigation.navigate('Rewards');
                  }}>
                  <Ionicons
                    name="gift-outline"
                    size={20}
                    color={isDarkMode ? '#fff' : '#000'}
                  />
                  <Text
                    style={[
                      styles.menuText,
                      isDarkMode && styles.menuTextDark,
                    ]}>
                    Refer & Earn
                  </Text>
                </TouchableOpacity> */}

                {/* Raise Query */}

                {/* <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setMenuVisible(false);    
                    navigation.navigate("RaiseQuery");
                  }}>
                  <Ionicons
                    name="help-circle-outline"
                    size={20}
                    color={isDarkMode ? "#fff" : "#000"}
                  />
                  <Text
                    style={[
                      styles.menuText,
                      isDarkMode && styles.menuTextDark,
                    ]}>
                    Raise Query
                  </Text>
                </TouchableOpacity> */}

                {/* FAQ  */}

                {/* <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setMenuVisible(false);
                    navigation.navigate('FAQScreen');
                  }}>
                  <Ionicons
                    name="information-circle-outline"
                    size={20}
                    color={isDarkMode ? '#fff' : '#000'}
                  />
                  <Text style={[styles.menuText, isDarkMode && styles.menuTextDark]}>
                    FAQs
                  </Text>
                </TouchableOpacity> */}

                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={handleLogout}>
                  <Ionicons
                    name="log-out-outline"
                    size={20}
                    color={isDarkMode ? "#fff" : "#000"}
                  />
                  <Text
                    style={[
                      styles.menuText,
                      isDarkMode && styles.menuTextDark,
                    ]}>
                    Logout
                  </Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <TouchableOpacity style={styles.menuItem} onPress={handleLogin}>
                  <Ionicons
                    name="log-in-outline"
                    size={20}
                    color={isDarkMode ? "#fff" : "#000"}
                  />
                  <Text
                    style={[
                      styles.menuText,
                      isDarkMode && styles.menuTextDark,
                    ]}>
                    Login
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={handleSignup}>
                  <Ionicons
                    name="person-add-outline"
                    size={20}
                    color={isDarkMode ? "#fff" : "#000"}
                  />
                  <Text
                    style={[
                      styles.menuText,
                      isDarkMode && styles.menuTextDark,
                    ]}>
                    Sign Up
                  </Text>
                </TouchableOpacity>
              </>
            )}

            <View style={styles.divider} />

            <TouchableOpacity
              style={styles.menuItem}
              onPress={handleThemeMenuToggle}>
              <Ionicons
                name={getThemeIcon(themeMode)}
                size={20}
                color={isDarkMode ? "#fff" : "#000"}
              />
              <Text style={[styles.menuText, isDarkMode && styles.menuTextDark]}>
                Theme: {getThemeLabel(themeMode)}
              </Text>
              <Ionicons
                name={themeMenuVisible ? "chevron-up" : "chevron-down"}
                size={16}
                color={isDarkMode ? "#fff" : "#000"}
                style={{ marginLeft: 'auto' }}
              />
            </TouchableOpacity>

            {themeMenuVisible && (
              <View style={styles.subMenu}>
                <TouchableOpacity
                  style={[styles.subMenuItem, themeMode === 'system' && styles.selectedMenuItem]}
                  onPress={() => handleThemeSelection('system')}>
                  <Ionicons
                    name="phone-portrait-outline"
                    size={18}
                    color={isDarkMode ? "#fff" : "#000"}
                  />
                  <Text style={[styles.subMenuText, isDarkMode && styles.menuTextDark]}>
                    System
                  </Text>
                  {themeMode === 'system' && (
                    <Ionicons
                      name="checkmark"
                      size={16}
                      color="#4CAF50"
                      style={{ marginLeft: 'auto' }}
                    />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.subMenuItem, themeMode === 'light' && styles.selectedMenuItem]}
                  onPress={() => handleThemeSelection('light')}>
                  <Ionicons
                    name="sunny-outline"
                    size={18}
                    color={isDarkMode ? "#fff" : "#000"}
                  />
                  <Text style={[styles.subMenuText, isDarkMode && styles.menuTextDark]}>
                    Light
                  </Text>
                  {themeMode === 'light' && (
                    <Ionicons
                      name="checkmark"
                      size={16}
                      color="#4CAF50"
                      style={{ marginLeft: 'auto' }}
                    />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.subMenuItem, themeMode === 'dark' && styles.selectedMenuItem]}
                  onPress={() => handleThemeSelection('dark')}>
                  <Ionicons
                    name="moon-outline"
                    size={18}
                    color={isDarkMode ? "#fff" : "#000"}
                  />
                  <Text style={[styles.subMenuText, isDarkMode && styles.menuTextDark]}>
                    Dark
                  </Text>
                  {themeMode === 'dark' && (
                    <Ionicons
                      name="checkmark"
                      size={16}
                      color="#4CAF50"
                      style={{ marginLeft: 'auto' }}
                    />
                  )}
                </TouchableOpacity>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // Ensure content starts from the left
    justifyContent: 'flex-start',
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  cartIcon: {
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  menuContainer: {
    position: "absolute",
    top: 50,
    right: 20,
    backgroundColor: "#fff",
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 4,
    minWidth: 150,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  menuContainerDark: {
    backgroundColor: "#333",
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  menuText: {
    marginLeft: 12,
    fontSize: 16,
    color: "#000",
  },
  menuTextDark: {
    color: "#fff",
  },
  divider: {
    height: 1,
    backgroundColor: "#ddd",
    marginVertical: 8,
  },
  subMenu: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    marginHorizontal: 8,
    borderRadius: 6,
    paddingVertical: 4,
  },
  subMenuItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 4,
  },
  selectedMenuItem: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  subMenuText: {
    marginLeft: 10,
    fontSize: 14,
    color: "#000",
  },
});
