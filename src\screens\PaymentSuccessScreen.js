import React, { useContext, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';
import LottieView from 'lottie-react-native';

const { width } = Dimensions.get('window');

const PaymentSuccessScreen = ({ route, navigation }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const { 
    paymentData, 
    orderAmount, 
    isCartPayment, 
    items = [] 
  } = route.params || {};

  useEffect(() => {
    // Auto-navigate to home after 10 seconds
    const timer = setTimeout(() => {
      navigation.navigate('HomeScreen');
    }, 10000);

    return () => clearTimeout(timer);
  }, [navigation]);

  const formatPrice = (price) => {
    return `₹${(price / 100).toLocaleString('en-IN')}`;
  };

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleContinueShopping = () => {
    navigation.navigate('PackagesScreen');
  };

  const handleGoHome = () => {
    navigation.navigate('HomeScreen');
  };

  const handleViewOrders = () => {
    // Navigate to orders screen when implemented
    navigation.navigate('Profile');
  };

  return (
    <ScrollView style={[
      styles.container,
      isDarkMode && styles.containerDark
    ]}>
      <View style={styles.content}>
        {/* Success Animation */}
        <View style={styles.animationContainer}>
          <LottieView
            source={require('../assets/confetti.json')}
            autoPlay
            loop={false}
            style={styles.animation}
          />
          <View style={[
            styles.successIcon,
            isDarkMode && styles.successIconDark
          ]}>
            <Icon
              name="check"
              size={40}
              color="#fff"
            />
          </View>
        </View>

        {/* Success Message */}
        <Text style={[
          styles.successTitle,
          isDarkMode && styles.successTitleDark
        ]}>
          Payment Successful!
        </Text>

        <Text style={[
          styles.successSubtitle,
          isDarkMode && styles.successSubtitleDark
        ]}>
          Thank you for your purchase. Your order has been confirmed.
        </Text>

        {/* Order Details Card */}
        <View style={[
          styles.orderCard,
          isDarkMode && styles.orderCardDark
        ]}>
          <View style={styles.orderHeader}>
            <Text style={[
              styles.orderTitle,
              isDarkMode && styles.textDark
            ]}>
              Order Details
            </Text>
            <View style={styles.orderBadge}>
              <Text style={styles.orderBadgeText}>PAID</Text>
            </View>
          </View>

          <View style={styles.orderInfo}>
            <View style={styles.orderRow}>
              <Text style={[
                styles.orderLabel,
                isDarkMode && styles.orderLabelDark
              ]}>
                Payment ID:
              </Text>
              <Text style={[
                styles.orderValue,
                isDarkMode && styles.textDark
              ]}>
                {paymentData?.razorpay_payment_id || 'N/A'}
              </Text>
            </View>

            <View style={styles.orderRow}>
              <Text style={[
                styles.orderLabel,
                isDarkMode && styles.orderLabelDark
              ]}>
                Order ID:
              </Text>
              <Text style={[
                styles.orderValue,
                isDarkMode && styles.textDark
              ]}>
                {paymentData?.razorpay_order_id || 'N/A'}
              </Text>
            </View>

            <View style={styles.orderRow}>
              <Text style={[
                styles.orderLabel,
                isDarkMode && styles.orderLabelDark
              ]}>
                Amount Paid:
              </Text>
              <Text style={[
                styles.orderValue,
                styles.amountText,
                isDarkMode && styles.textDark
              ]}>
                {formatPrice(orderAmount)}
              </Text>
            </View>

            <View style={styles.orderRow}>
              <Text style={[
                styles.orderLabel,
                isDarkMode && styles.orderLabelDark
              ]}>
                Date & Time:
              </Text>
              <Text style={[
                styles.orderValue,
                isDarkMode && styles.textDark
              ]}>
                {formatDate(Date.now())}
              </Text>
            </View>
          </View>
        </View>

        {/* Items Purchased */}
        <View style={[
          styles.itemsCard,
          isDarkMode && styles.itemsCardDark
        ]}>
          <Text style={[
            styles.itemsTitle,
            isDarkMode && styles.textDark
          ]}>
            {isCartPayment ? 'Items Purchased' : 'Package Purchased'}
          </Text>

          {items.map((item, index) => (
            <View key={index} style={styles.itemRow}>
              <View style={styles.itemInfo}>
                <Text style={[
                  styles.itemName,
                  isDarkMode && styles.textDark
                ]}>
                  {item.name || item.packageName}
                </Text>
                {item.quantity && (
                  <Text style={[
                    styles.itemQuantity,
                    isDarkMode && styles.itemQuantityDark
                  ]}>
                    Qty: {item.quantity}
                  </Text>
                )}
              </View>
              <Text style={[
                styles.itemPrice,
                isDarkMode && styles.textDark
              ]}>
                ₹{item.price || item.originalPrice}
              </Text>
            </View>
          ))}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[
              styles.primaryButton,
              isDarkMode && styles.primaryButtonDark
            ]}
            onPress={handleGoHome}
            activeOpacity={0.8}
          >
            <Icon name="home" size={16} color="#fff" style={styles.buttonIcon} />
            <Text style={styles.primaryButtonText}>Go to Home</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.secondaryButton,
              isDarkMode && styles.secondaryButtonDark
            ]}
            onPress={handleContinueShopping}
            activeOpacity={0.8}
          >
            <Icon name="shopping-bag" size={16} color={isDarkMode ? '#4CAF50' : '#198754'} style={styles.buttonIcon} />
            <Text style={[
              styles.secondaryButtonText,
              isDarkMode && styles.secondaryButtonTextDark
            ]}>
              Continue Shopping
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tertiaryButton,
              isDarkMode && styles.tertiaryButtonDark
            ]}
            onPress={handleViewOrders}
            activeOpacity={0.8}
          >
            <Text style={[
              styles.tertiaryButtonText,
              isDarkMode && styles.tertiaryButtonTextDark
            ]}>
              View My Orders
            </Text>
          </TouchableOpacity>
        </View>

        {/* Auto-redirect Notice */}
        <Text style={[
          styles.autoRedirectText,
          isDarkMode && styles.autoRedirectTextDark
        ]}>
          You will be automatically redirected to home in 10 seconds
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  containerDark: {
    backgroundColor: '#121212',
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  animationContainer: {
    position: 'relative',
    alignItems: 'center',
    marginBottom: 20,
  },
  animation: {
    width: 200,
    height: 200,
  },
  successIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginTop: -30,
    marginLeft: -30,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#198754',
    justifyContent: 'center',
    alignItems: 'center',
  },
  successIconDark: {
    backgroundColor: '#4CAF50',
  },
  successTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#198754',
    textAlign: 'center',
    marginBottom: 8,
  },
  successTitleDark: {
    color: '#4CAF50',
  },
  successSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  successSubtitleDark: {
    color: '#999',
  },
  orderCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: width - 40,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderCardDark: {
    backgroundColor: '#1e1e1e',
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  orderTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
  },
  orderBadge: {
    backgroundColor: '#198754',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  orderBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  orderInfo: {
    gap: 12,
  },
  orderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderLabel: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  orderLabelDark: {
    color: '#999',
  },
  orderValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    textAlign: 'right',
  },
  amountText: {
    fontSize: 16,
    color: '#198754',
  },
  textDark: {
    color: '#fff',
  },
  itemsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: width - 40,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemsCardDark: {
    backgroundColor: '#1e1e1e',
  },
  itemsTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 16,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  itemQuantity: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  itemQuantityDark: {
    color: '#999',
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#198754',
  },
  actionButtons: {
    width: width - 40,
    gap: 12,
  },
  primaryButton: {
    backgroundColor: '#198754',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    borderRadius: 8,
  },
  primaryButtonDark: {
    backgroundColor: '#4CAF50',
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#198754',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
    borderRadius: 8,
  },
  secondaryButtonDark: {
    borderColor: '#4CAF50',
  },
  secondaryButtonText: {
    color: '#198754',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonTextDark: {
    color: '#4CAF50',
  },
  tertiaryButton: {
    backgroundColor: 'transparent',
    paddingVertical: 12,
    alignItems: 'center',
  },
  tertiaryButtonDark: {
    backgroundColor: 'transparent',
  },
  tertiaryButtonText: {
    color: '#666',
    fontSize: 16,
    textDecorationLine: 'underline',
  },
  tertiaryButtonTextDark: {
    color: '#999',
  },
  buttonIcon: {
    marginRight: 8,
  },
  autoRedirectText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    marginTop: 20,
    fontStyle: 'italic',
  },
  autoRedirectTextDark: {
    color: '#666',
  },
});

export default PaymentSuccessScreen;
