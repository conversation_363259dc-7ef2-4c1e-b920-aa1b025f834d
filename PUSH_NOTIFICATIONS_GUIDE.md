# 🔔 Expo Push Notifications - Production Ready

## 📱 Overview
Production-ready Expo Push Notifications with rich media support (images) and comprehensive background/foreground handling.

## 🚀 Quick Start

### 1. Get Your Push Token
1. Run the app on a **physical device** (not simulator/emulator)
2. Check the console output - your token and ready-to-use cURL command will be displayed
3. Copy the cURL command from console and test immediately

### 2. Console Output
When the app starts, you'll see:
```
================================================================================
🔔 EXPO PUSH TOKEN READY FOR TESTING
================================================================================
📱 Token: ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]

📋 Ready-to-use cURL command:
curl -X POST https://exp.host/--/api/v2/push/send \
-H "Content-Type: application/json" \
-d '{
  "to": "ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]",
  "title": "🔔 Test Notification",
  "body": "This is a test sent from cURL!",
  "data": {
    "image": "https://placekitten.com/300/300"
  }
}'
================================================================================
```

## 📋 Example Payloads

### 1. Basic Notification
```json
{
  "to": "ExponentPushToken[YOUR_TOKEN_HERE]",
  "title": "Hello from Shashtrarth!",
  "body": "This is a basic test notification.",
  "sound": "default",
  "badge": 1
}
```

### 2. Notification with Image
```json
{
  "to": "ExponentPushToken[YOUR_TOKEN_HERE]",
  "title": "🎓 New Exam Available!",
  "body": "Banking Exam Mock Test is now available. Start practicing!",
  "data": {
    "type": "exam_reminder",
    "examId": "banking_001",
    "image": "https://images.unsplash.com/photo-*************-0b793f4b4173?w=500",
    "actionUrl": "app://exam/banking_001",
    "actionText": "Start Exam"
  },
  "sound": "default",
  "badge": 1
}
```

### 3. Friend Progress Notification
```json
{
  "to": "ExponentPushToken[YOUR_TOKEN_HERE]",
  "title": "👥 Friend Update",
  "body": "Anjali just completed Group B level! You're next!",
  "data": {
    "type": "friend_progress",
    "friendId": "anjali_123",
    "image": "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=500",
    "actionUrl": "app://progress/friends",
    "actionText": "View Progress"
  },
  "sound": "default",
  "badge": 1
}
```

### 4. Package Promotion with Image
```json
{
  "to": "ExponentPushToken[YOUR_TOKEN_HERE]",
  "title": "🎁 Special Offer!",
  "body": "Get 50% off on Premium Package. Limited time offer!",
  "data": {
    "type": "package_promotion",
    "packageId": "premium_001",
    "image": "https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=500",
    "actionUrl": "app://packages/premium_001",
    "actionText": "View Offer",
    "discount": "50%"
  },
  "sound": "default",
  "badge": 1
}
```

### 5. Study Reminder with Custom Image
```json
{
  "to": "ExponentPushToken[YOUR_TOKEN_HERE]",
  "title": "📚 Time to Study!",
  "body": "You haven't practiced today. Keep your streak going!",
  "data": {
    "type": "study_reminder",
    "image": "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500",
    "actionUrl": "app://practice/daily",
    "actionText": "Start Practice",
    "streakDays": 5
  },
  "sound": "default",
  "badge": 1
}
```

## 🧪 Testing Steps

### Using the Console cURL Command:
1. Run the app on a physical device
2. Copy the complete cURL command from the console
3. Paste and run it in your terminal
4. Check your device for the notification!

### Using Postman:
1. Create a new POST request
2. Set URL to: `https://exp.host/--/api/v2/push/send`
3. Add headers:
   - `Content-Type: application/json`
4. Use the example payload below with your token
5. Send the request

### Custom cURL Example:
```bash
curl -X POST https://exp.host/--/api/v2/push/send \
-H "Content-Type: application/json" \
-d '{
  "to": "ExponentPushToken[YOUR_TOKEN_HERE]",
  "title": "🔔 Test Notification",
  "body": "This is a test sent from cURL!",
  "data": {
    "image": "https://placekitten.com/300/300"
  }
}'
```

## 📱 App Behavior & Console Logs

### Foreground Notifications:
- Shows an alert dialog with title and body
- Detailed console logging with formatted output
- Option to "View" or "Dismiss"

### Background/Killed App:
- Shows system notification
- Tapping opens the app and logs interaction
- Handles app launch from notification

### Console Output Examples:

#### When notification is received (foreground):
```
📨 FOREGROUND NOTIFICATION RECEIVED
──────────────────────────────────────────────────
📋 Title: 🔔 Test Notification
📋 Body: This is a test sent from cURL!
📋 Image URL: https://placekitten.com/300/300
📋 Data: {
  "image": "https://placekitten.com/300/300"
}
──────────────────────────────────────────────────
```

#### When notification is tapped (background):
```
👆 NOTIFICATION TAPPED (Background/Killed App)
──────────────────────────────────────────────────
📋 Title: 🔔 Test Notification
📋 Body: This is a test sent from cURL!
📋 Image URL: https://placekitten.com/300/300
📋 Data: {
  "image": "https://placekitten.com/300/300"
}
📋 Action Origin: default
──────────────────────────────────────────────────
```

#### When app is launched from notification:
```
🚀 APP LAUNCHED FROM NOTIFICATION
──────────────────────────────────────────────────
📋 Title: 🔔 Test Notification
📋 Body: This is a test sent from cURL!
📋 Image URL: https://placekitten.com/300/300
📋 Data: {
  "image": "https://placekitten.com/300/300"
}
──────────────────────────────────────────────────
```

### Image Handling:
- Images are loaded from `data.image` field
- Supports common formats (JPG, PNG, WebP)
- Console logs image URLs for debugging

## 🔧 Customization

### Notification Types:
The app handles different notification types based on `data.type`:
- `exam_reminder` - Navigate to exam screen
- `friend_progress` - Navigate to progress screen
- `package_promotion` - Navigate to packages screen
- `study_reminder` - Navigate to practice screen

### Image Requirements:
- **Format**: JPG, PNG, WebP
- **Size**: Recommended 500px width
- **URL**: Must be publicly accessible
- **HTTPS**: Required for security

### Data Fields:
- `type` - Notification category
- `image` - Image URL for rich media
- `actionUrl` - Deep link or navigation target
- `actionText` - Button text for action
- Custom fields for your app logic

## 🐛 Troubleshooting

### No Push Token:
- Ensure you're using a physical device
- Check device permissions for notifications
- Restart the app and check console logs

### Notifications Not Received:
- Verify the push token is correct
- Check Expo's push service status
- Ensure proper JSON formatting in payload

### Images Not Loading:
- Verify image URL is publicly accessible
- Check image format (JPG, PNG, WebP)
- Ensure HTTPS protocol is used

### Console Logs to Check:
```
🔔 Initializing push notifications...
✅ Push notifications initialized successfully
📱 Expo Push Token: ExponentPushToken[...]
📨 Foreground notification received: {...}
👆 Notification tapped: {...}
🖼️ Notification contains image: https://...
```

## 🚀 Production Considerations

### Backend Integration:
```javascript
// Example: Send token to your backend
const sendTokenToBackend = async (token) => {
  await fetch('YOUR_API_URL/push-tokens', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      token,
      userId: 'current-user-id',
      platform: Platform.OS
    })
  });
};
```

### Batch Notifications:
```json
[
  {
    "to": "ExponentPushToken[TOKEN_1]",
    "title": "Batch Notification 1",
    "body": "Message for user 1"
  },
  {
    "to": "ExponentPushToken[TOKEN_2]",
    "title": "Batch Notification 2", 
    "body": "Message for user 2"
  }
]
```

## 📊 Analytics Integration

The app automatically logs notification events:
- `push_notifications_initialized`
- `notification_received`
- `notification_opened`
- `notification_action_taken`

## 🔗 Useful Links

- [Expo Push Notifications Docs](https://docs.expo.dev/push-notifications/overview/)
- [Expo Push Tool](https://expo.dev/notifications)
- [Push Notification Tester](https://expo.dev/tools)

---

**Note**: Always test on physical devices. Push notifications don't work on simulators/emulators!
