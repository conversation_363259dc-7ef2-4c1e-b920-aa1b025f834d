import React, { useContext } from 'react';
import { View, TouchableOpacity, StyleSheet, Text, Platform } from 'react-native';
import { useNavigation, useRoute, DrawerActions } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ThemeContext } from '../context/ThemeContext';
import { calculateTabBarHeight, getTabBarPadding } from '../utils/tabBarUtils';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const BottomTabBar = ({ children }) => {
  const { isDarkMode } = useContext(ThemeContext);
  const navigation = useNavigation();
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const currentTab = route.name?.split('Screen')[0] || '';

  const tabBarHeight = calculateTabBarHeight(insets);
  const tabBarPadding = getTabBarPadding(insets);

  const isTabActive = (tabName) => {
    return currentTab.toLowerCase().includes(tabName.toLowerCase());
  };  const navigateTo = (screen) => {
    // Navigate through the MainTabs navigator with correct screen naming
    switch (screen) {
      case 'ChatbotTab':
        navigation.navigate('MainTabs', {
          screen: 'ChatbotTab',
          params: {
            screen: 'AcharyaBot'
          }
        });
        break;
      case 'HomeTab':
        navigation.navigate('MainTabs', {
          screen: 'HomeTab',
          params: {
            screen: 'HomeScreen'
          }
        });
        break;
      case 'SavedTab':
        navigation.navigate('MainTabs', {
          screen: 'SavedTab',
          params: {
            screen: 'SavedTestSeries'
          }
        });
        break;
      default:
        navigation.navigate('MainTabs', {
          screen: screen
        });
    }
  };

  const getTabColor = (tabName) => {
    const active = isTabActive(tabName);
    return active ? '#198754' : isDarkMode ? '#666' : '#999';
  };

  return (
    <View style={styles.wrapper}>
      <View style={[styles.content, { marginBottom: tabBarHeight }]}>
        {children}
      </View>
      <View style={[
        styles.tabBar,
        {
          height: tabBarHeight,
          paddingTop: tabBarPadding.paddingTop,
          paddingBottom: tabBarPadding.paddingBottom,
          backgroundColor: isDarkMode ? '#1a1a1a' : '#fff',
          borderTopColor: isDarkMode ? '#333' : '#e0e0e0',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 8,
        }
      ]}>
        <TouchableOpacity 
          style={styles.tabItem} 
          onPress={() => navigateTo('HomeTab')}
        >
          <Ionicons
            name={isTabActive('home') ? 'home' : 'home-outline'}
            size={24}
            color={getTabColor('home')}
          />
          <Text style={[styles.tabLabel, { color: getTabColor('home') }]}>
            Home
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.tabItem}
          onPress={() => navigateTo('SavedTab')}
        >
          <Ionicons
            name={isTabActive('saved') ? 'bookmark' : 'bookmark-outline'}
            size={24}
            color={getTabColor('saved')}
          />
          <Text style={[styles.tabLabel, { color: getTabColor('saved') }]}>
            Saved
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.tabItem}
          onPress={() => navigateTo('ChatbotTab')}
        >
          <MaterialCommunityIcons
            name="robot"
            size={24}
            color={getTabColor('chatbot')}
          />
          <Text style={[styles.tabLabel, { color: getTabColor('chatbot') }]}>
            Acharya AI
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.tabItem}
          onPress={() => navigation.dispatch(DrawerActions.openDrawer())}
        >
          <Ionicons
            name={isTabActive('more') ? 'menu' : 'menu-outline'}
            size={24}
            color={getTabColor('more')}
          />
          <Text style={[styles.tabLabel, { color: getTabColor('more') }]}>
            More
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  content: {
    flex: 1,
    // marginBottom is now dynamic and applied inline
  },
  tabBar: {
    flexDirection: 'row',
    borderTopWidth: 1,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
    zIndex: 1000,
    // height, paddingTop, paddingBottom are now dynamic and applied inline
  },
  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 80,
    paddingVertical: 4,
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
    textAlign: 'center',
    letterSpacing: 0.2,
  }
});

export default BottomTabBar;
