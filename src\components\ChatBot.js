import React, { useState, useEffect, useRef, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Keyboard,
  Platform,
  SafeAreaView,
  KeyboardAvoidingView,
} from 'react-native';
import { ThemeContext } from '../context/ThemeContext';
import { useDispatch, useSelector } from 'react-redux';
import { sendChatMessage } from "../redux/chatBotSlice";
import { Ionicons } from '@expo/vector-icons';

const ChatBot = ({ onClose }) => {
  const dispatch = useDispatch();
  const { isDarkMode } = useContext(ThemeContext);
  const accessToken = useSelector((state) => state?.auth?.JWT_Token?.access);
  const [query, setQuery] = useState('');
  const [messages, setMessages] = useState([]);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const scrollViewRef = useRef(null);
  const inputRef = useRef(null);

  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (e) => {
        setKeyboardHeight(e.endCoordinates.height);
        scrollToBottom();
      }
    );
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  const scrollToBottom = () => {
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  const handleSend = async () => {
    if (!query.trim()) return;
    const newUserMessage = { text: query, type: 'user' };
    setMessages((prev) => [...prev, newUserMessage]);
    setQuery('');
    Keyboard.dismiss(); // Hide the keyboard after sending

    if (!accessToken) {
      setMessages((prev) => [
        ...prev,
        { text: '⚠️ Please login first to continue chatting with me.', type: 'bot' },
      ]);
      return;
    }

    try {
      const result = await dispatch(sendChatMessage({ query: query.trim() })).unwrap();
      const botResponses = result.results.map((r) => ({
        text: r.explanation,
        type: 'bot',
      }));
      setMessages((prev) => [...prev, ...botResponses]);
    } catch (error) {
      setMessages((prev) => [
        ...prev,
        { text: '❌ Something went wrong. Please try again later.', type: 'bot' },
      ]);
    }
  };

  return (
    <SafeAreaView style={[
      styles.safeArea,
      { backgroundColor: isDarkMode ? '#1a1a1a' : '#FFFFFF' }
    ]}>
      <KeyboardAvoidingView 
        style={[
          styles.container,
          { backgroundColor: isDarkMode ? '#1a1a1a' : '#FFFFFF' }
        ]} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        <View style={styles.innerContainer}>
          <ScrollView
            ref={scrollViewRef}
            style={[
              styles.messages,
              { backgroundColor: isDarkMode ? '#121212' : '#F5F5F5' }
            ]}
            contentContainerStyle={[
              styles.messagesContent,
              { paddingBottom: keyboardHeight + (Platform.OS === 'ios' ? 90 : 70) }
            ]}
          >
            {messages.map((msg, idx) => (
              <View
                key={idx}
                style={[
                  styles.message,
                  msg.type === 'user' 
                    ? [styles.userMessage, { backgroundColor: '#198754' }]
                    : [styles.botMessage, { 
                        backgroundColor: isDarkMode ? '#2a2a2a' : '#FFFFFF',
                        borderColor: isDarkMode ? '#404040' : '#E0E0E0',
                      }],
                ]}
              >
                <Text style={[
                  styles.messageText,
                  msg.type === 'user' 
                    ? styles.userMessageText
                    : [styles.botMessageText, { color: isDarkMode ? '#FFFFFF' : '#000000' }],
                ]}>
                  {msg.text}
                </Text>
              </View>
            ))}
          </ScrollView>
          <View style={[
            styles.inputArea,
            { 
              backgroundColor: isDarkMode ? '#1a1a1a' : '#FFFFFF',
              borderTopColor: isDarkMode ? '#404040' : '#E0E0E0'
            }
          ]}>
            <TextInput
              ref={inputRef}
              style={[
                styles.input,
                {
                  backgroundColor: isDarkMode ? '#2a2a2a' : '#F5F5F5',
                  borderColor: isDarkMode ? '#404040' : '#E0E0E0',
                  color: isDarkMode ? '#FFFFFF' : '#000000'
                }
              ]}
              placeholder="Ask me anything..."
              placeholderTextColor={isDarkMode ? '#888888' : '#999999'}
              value={query}
              onChangeText={setQuery}
              multiline={true}
              maxHeight={100}
              returnKeyType="send"
              blurOnSubmit={false}
              onSubmitEditing={handleSend}
            />
            <TouchableOpacity 
              onPress={handleSend} 
              style={styles.sendButton}
              activeOpacity={0.7}
            >
              <Icon name="send" size={24} color="#198754" />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1
  },
  container: {
    flex: 1
  },
  innerContainer: {
    flex: 1
  },
  messages: {
    flex: 1
  },
  messagesContent: {
    padding: 12
  },
  message: {
    padding: 12,
    borderRadius: 20,
    maxWidth: '80%',
    marginVertical: 4
  },
  userMessage: {
    alignSelf: 'flex-end',
    borderBottomRightRadius: 4,
    marginLeft: '20%'
  },
  botMessage: {
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 4,
    marginRight: '20%',
    borderWidth: 1
  },
  messageText: {
    fontSize: 15,
    lineHeight: 20
  },
  userMessageText: {
    color: '#FFFFFF'
  },
  botMessageText: {
    color: '#000000'
  },
  inputArea: {
    flexDirection: 'row',
    padding: 8,
    borderTopWidth: 1,
    paddingBottom: Platform.OS === 'ios' ? 25 : 8,
    alignItems: 'flex-end'
  },  input: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 150,
    fontSize: 15,
    maxHeight: 100,
    minHeight: 40
  },
  sendButton: {
    padding: 8,
    alignSelf: 'flex-end',
    marginBottom: 150
  }
});

export default ChatBot;
