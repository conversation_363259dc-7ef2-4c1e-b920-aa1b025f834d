# 🌍 Environment Configuration Consolidation Guide

## 📋 Overview

This guide documents the consolidation of multiple environment files into a single, manageable `.env` file and the update of EAS configuration to use environment variables instead of hardcoded values.

## 🔄 Changes Made

### ✅ Files Consolidated
- **Removed**: `.env.development` (redundant)
- **Updated**: `.env` (now contains all environment variables)
- **Updated**: `eas.json` (now uses environment variables)
- **Maintained**: `app.config.js` (already using environment variables correctly)

### 📁 New File Structure
```
├── .env                    # Single source of truth for all environment variables
├── eas.json               # Uses environment variables instead of hardcoded values
├── app.config.js          # Already configured correctly
└── ENVIRONMENT_CONSOLIDATION_GUIDE.md  # This documentation
```

## 🔧 Environment Variable Configuration

### 📝 .env File Structure

The consolidated `.env` file now contains:

#### 🏗️ Environment Type
```bash
NODE_ENV=development
EXPO_DEBUG=true
```

#### 🌐 API Configuration
```bash
# Development URLs (default)
EXPO_PUBLIC_WEBSITE_DOMAIN=https://f0kqclt5-8000.inc1.devtunnels.ms/
EXPO_PUBLIC_BASE_URL=https://f0kqclt5-8000.inc1.devtunnels.ms/
EXPO_PUBLIC_BLOGS=api/blogs/public-blogs/

# Production URLs (commented out, uncomment for production)
# EXPO_PUBLIC_WEBSITE_DOMAIN=https://api.shashtrarth.com
# EXPO_PUBLIC_BASE_URL=https://api.shashtrarth.com/
```

#### 🔥 Firebase Configuration
```bash
EXPO_PUBLIC_FIREBASE_API_KEY=AIzaSyBJSLcv459JwCzNhkgOndDQ7wzuAx81YwU
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=shashtrarth-fcm.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=shashtrarth-fcm
# ... (all Firebase variables)
```

#### 🔐 Google Auth Configuration
```bash
EXPO_PUBLIC_ANDROID_CLIENT_ID=************-i2v0h6n8q9f3v4q5r6s7t8u9v0w1x2y3.apps.googleusercontent.com
EXPO_PUBLIC_IOS_CLIENT_ID=************-a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6.apps.googleusercontent.com
# ... (all Google Auth variables)
```

#### 🏗️ EAS Build Configuration
```bash
# Development Build URLs
EAS_DEV_WEBSITE_DOMAIN=https://f0kqclt5-8000.inc1.devtunnels.ms/
EAS_DEV_BASE_URL=https://f0kqclt5-8000.inc1.devtunnels.ms/

# Production Build URLs
EAS_PROD_WEBSITE_DOMAIN=https://api.shashtrarth.com
EAS_PROD_BASE_URL=https://api.shashtrarth.com/
```

## 🚀 EAS Configuration Updates

### 📱 Build Profiles

The `eas.json` now uses environment variables for all build profiles:

#### 🔧 Development Build
```json
"development": {
  "developmentClient": true,
  "distribution": "internal",
  "env": {
    "EXPO_PUBLIC_WEBSITE_DOMAIN": "$EAS_DEV_WEBSITE_DOMAIN",
    "EXPO_PUBLIC_BASE_URL": "$EAS_DEV_BASE_URL",
    // ... all other variables from .env
  }
}
```

#### 🔍 Preview Build
```json
"preview": {
  "distribution": "internal",
  "env": {
    "EXPO_PUBLIC_WEBSITE_DOMAIN": "$EAS_DEV_WEBSITE_DOMAIN",
    "EXPO_PUBLIC_BASE_URL": "$EAS_DEV_BASE_URL",
    // ... uses development URLs for testing
  }
}
```

#### 🏭 Production Build
```json
"production": {
  "autoIncrement": true,
  "env": {
    "EXPO_PUBLIC_WEBSITE_DOMAIN": "$EAS_PROD_WEBSITE_DOMAIN",
    "EXPO_PUBLIC_BASE_URL": "$EAS_PROD_BASE_URL",
    // ... uses production URLs
  }
}
```

## 🎯 Benefits

### ✅ Improved Maintainability
- **Single source of truth**: All environment variables in one file
- **No duplication**: Eliminated redundant `.env.development`
- **Clear separation**: Development vs Production configurations
- **Easy switching**: Comment/uncomment lines to switch environments

### 🔒 Enhanced Security
- **Environment-specific builds**: Different URLs for different build types
- **No hardcoded secrets**: All sensitive data in environment variables
- **Version control safe**: Easy to exclude sensitive values

### 🚀 Better Developer Experience
- **Consistent configuration**: Same pattern across all files
- **Clear documentation**: Well-commented environment variables
- **Easy setup**: Copy `.env` to `.env.local` for local overrides

## 📖 Usage Instructions

### 🏠 Local Development
1. Use the default `.env` file as-is for development
2. Create `.env.local` for personal overrides (gitignored)
3. Run `expo start` - variables are automatically loaded

### 🏗️ EAS Builds
1. **Development**: `eas build --profile development`
   - Uses `EAS_DEV_*` variables from `.env`
2. **Preview**: `eas build --profile preview`
   - Uses development URLs for testing
3. **Production**: `eas build --profile production`
   - Uses `EAS_PROD_*` variables from `.env`

### 🔄 Environment Switching
To switch from development to production:
1. Comment out development URLs in `.env`
2. Uncomment production URLs in `.env`
3. Restart the development server

## 🛠️ Configuration Files

### 📄 app.config.js
Already correctly configured to read from `process.env`:
```javascript
extra: {
  EXPO_PUBLIC_WEBSITE_DOMAIN: process.env.EXPO_PUBLIC_WEBSITE_DOMAIN,
  EXPO_PUBLIC_BASE_URL: process.env.EXPO_PUBLIC_BASE_URL,
  // ... all other variables
}
```

### 📄 .gitignore
Ensure these patterns are in `.gitignore`:
```
# local env files
.env*.local
```

## 🔍 Troubleshooting

### ❌ Common Issues

1. **Variables not loading**: Restart the development server
2. **Build failing**: Check that all required variables are set in `.env`
3. **Wrong API URLs**: Verify the correct URLs are uncommented in `.env`

### ✅ Verification Steps

1. Check environment variables are loaded:
   ```javascript
   console.log('API URL:', process.env.EXPO_PUBLIC_BASE_URL);
   ```

2. Verify EAS build configuration:
   ```bash
   eas build --profile development --dry-run
   ```

## 📚 Best Practices

1. **Never commit sensitive data**: Use `.env.local` for secrets
2. **Document all variables**: Add comments explaining each variable
3. **Use consistent naming**: Follow the `EXPO_PUBLIC_*` pattern
4. **Test all profiles**: Verify development, preview, and production builds
5. **Keep backups**: Save important configuration values securely

## 🎉 Migration Complete

The environment configuration has been successfully consolidated! The project now uses:
- ✅ Single `.env` file for all environment variables
- ✅ Environment-aware EAS build configuration
- ✅ Consistent variable naming and structure
- ✅ Clear documentation and usage instructions

This setup provides better maintainability, security, and developer experience while following Expo best practices.
