# 🧪 Cart & Payment System Testing Guide

## Quick Start Testing

### 1. Add Test Component (Optional)
To quickly test cart functionality, temporarily add the test component to any screen:

```javascript
// Add to any screen for testing (e.g., HomeScreen.js)
import CartTestComponent from '../components/CartTestComponent';

// Add in render method
<CartTestComponent />
```

**⚠️ Remember to remove this component before production!**

### 2. Basic Cart Testing

#### Test Cart Operations:
1. **Navigate to Packages Screen**
   - Go to Packages tab
   - Find any package
   - Click "Add to Cart" button
   - Verify toast notification appears
   - Check cart icon in header shows item count

2. **Access Cart Screen**
   - Click cart icon in header
   - Verify cart screen opens
   - Check item appears in cart
   - Verify item details are correct

3. **Test Quantity Controls**
   - Use + and - buttons to change quantity
   - Verify total updates automatically
   - Test maximum quantity limits
   - Try to reduce quantity to 0 (should remove item)

4. **Test Item Removal**
   - Click trash icon on cart item
   - Confirm removal in alert dialog
   - Verify item is removed from cart
   - Check cart icon updates

5. **Test Cart Clearing**
   - Add multiple items to cart
   - Click "Clear" button in cart header
   - Confirm clearing in alert dialog
   - Verify all items are removed

### 3. Payment Flow Testing

#### Test Complete Purchase Flow:
1. **Prepare Cart**
   - Add 1-2 packages to cart
   - Navigate to cart screen
   - Verify items and total are correct

2. **Initiate Checkout**
   - Click "Proceed to Payment" button
   - Verify navigation to PaymentScreen
   - Check order summary displays correctly

3. **Test Coupon/Gift Card (Optional)**
   - Enter test coupon code
   - Click "Apply" button
   - Verify discount is applied
   - Test invalid codes for error handling

4. **Complete Payment**
   - Click "Pay Now" button
   - Razorpay payment sheet should open
   - Use test payment credentials:
     - Card: 4111 1111 1111 1111
     - Expiry: Any future date
     - CVV: Any 3 digits
     - Name: Any name

5. **Verify Success Flow**
   - Payment should complete successfully
   - Should navigate to PaymentSuccessScreen
   - Verify order details are displayed
   - Check cart is cleared after success
   - Test navigation buttons

### 4. Error Handling Testing

#### Test Payment Failures:
1. **Cancel Payment**
   - Start payment process
   - Cancel in Razorpay sheet
   - Verify navigation to PaymentFailureScreen
   - Check error message and retry option

2. **Network Error Simulation**
   - Turn off internet during payment
   - Verify proper error handling
   - Check retry functionality

3. **Invalid Coupon Testing**
   - Enter invalid coupon codes
   - Verify error messages appear
   - Check that discount is not applied

### 5. Persistence Testing

#### Test Cart Persistence:
1. **Add Items and Close App**
   - Add several items to cart
   - Close the app completely
   - Reopen the app
   - Navigate to cart
   - Verify all items are restored

2. **Test Across Navigation**
   - Add items to cart
   - Navigate to different screens
   - Return to cart
   - Verify items are still there

### 6. Navigation Testing

#### Test All Navigation Paths:
1. **From Packages to Cart**
   - Packages → Add to Cart → Cart Icon → Cart Screen

2. **From Cart to Payment**
   - Cart Screen → Proceed to Payment → Payment Screen

3. **Payment to Success**
   - Payment Screen → Complete Payment → Success Screen

4. **Success Navigation**
   - Success Screen → Go Home / Continue Shopping / View Orders

5. **Back Navigation**
   - Test back button on all screens
   - Verify proper navigation stack

### 7. UI/UX Testing

#### Test Visual Elements:
1. **Theme Consistency**
   - Switch between light and dark modes
   - Verify all screens adapt properly
   - Check color consistency

2. **Loading States**
   - Verify loading spinners appear during operations
   - Check loading states don't persist

3. **Empty States**
   - Test empty cart screen
   - Verify empty order history
   - Check call-to-action buttons work

4. **Responsive Design**
   - Test on different screen sizes
   - Verify layouts adapt properly
   - Check text doesn't overflow

### 8. Performance Testing

#### Test App Performance:
1. **Large Cart Testing**
   - Add many items to cart (10+)
   - Verify smooth scrolling
   - Check memory usage

2. **Rapid Operations**
   - Quickly add/remove items
   - Test rapid quantity changes
   - Verify state updates correctly

3. **Background/Foreground**
   - Add items to cart
   - Put app in background
   - Return to foreground
   - Verify cart state is maintained

## 🐛 Common Issues & Solutions

### Issue: Cart icon doesn't show item count
**Solution**: Check if cartSlice is properly added to Redux store

### Issue: Payment screen doesn't open
**Solution**: Verify navigation is properly configured in TabNavigator.js

### Issue: Items don't persist after app restart
**Solution**: Check Redux Persist configuration and AsyncStorage permissions

### Issue: Razorpay payment fails
**Solution**: Verify Razorpay configuration and test credentials

### Issue: Toast notifications don't appear
**Solution**: Ensure react-native-toast-message is properly configured

## 📱 Device Testing

### Test on Multiple Devices:
- **iOS Simulator**: Test iOS-specific behavior
- **Android Emulator**: Test Android-specific behavior
- **Physical Devices**: Test real-world performance
- **Different Screen Sizes**: Test responsive design

### Test Different Scenarios:
- **Low Memory**: Test with limited device memory
- **Slow Network**: Test with poor connectivity
- **Background Apps**: Test with other apps running
- **Different OS Versions**: Test compatibility

## ✅ Testing Checklist

### Core Functionality:
- [ ] Add items to cart
- [ ] Remove items from cart
- [ ] Update item quantities
- [ ] Clear entire cart
- [ ] Cart persistence across app restarts
- [ ] Cart icon shows correct count
- [ ] Navigation to cart screen
- [ ] Empty cart state

### Payment Flow:
- [ ] Navigate from cart to payment
- [ ] Order summary displays correctly
- [ ] Coupon code application
- [ ] Gift card application
- [ ] Payment initiation
- [ ] Payment success handling
- [ ] Payment failure handling
- [ ] Cart clearing after success

### UI/UX:
- [ ] Light/dark theme support
- [ ] Loading states
- [ ] Error states
- [ ] Toast notifications
- [ ] Responsive design
- [ ] Accessibility features

### Navigation:
- [ ] All screen transitions work
- [ ] Back navigation works
- [ ] Tab bar visibility rules
- [ ] Deep linking support

### Performance:
- [ ] Smooth animations
- [ ] Fast loading times
- [ ] Memory usage acceptable
- [ ] No memory leaks

## 🚀 Production Readiness

### Before Going Live:
1. **Remove Test Components**
   - Remove CartTestComponent
   - Remove any test data or mock functions

2. **Configure Production APIs**
   - Update API endpoints
   - Set production Razorpay keys
   - Configure error tracking

3. **Final Testing**
   - Test with production API
   - Verify payment flow with real transactions
   - Test error scenarios

4. **Performance Optimization**
   - Optimize images and assets
   - Minimize bundle size
   - Test on low-end devices

### Monitoring Setup:
- **Error Tracking**: Monitor payment failures
- **Analytics**: Track cart conversion rates
- **Performance**: Monitor app performance metrics
- **User Feedback**: Collect user experience feedback

---

**Happy Testing! 🎉**

Remember to test thoroughly on both iOS and Android platforms, and always test the complete user journey from adding items to cart through successful payment completion.
