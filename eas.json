﻿{
  "cli": {
    "version": ">= 16.6.2",
    "appVersionSource": "remote"
  },
  "build": {
    "development": {
      "distribution": "internal",
      "env": {
        "EXPO_PUBLIC_WEBSITE_DOMAIN": "$EAS_DEV_WEBSITE_DOMAIN",
        "EXPO_PUBLIC_BASE_URL": "$EAS_DEV_BASE_URL",
        "EXPO_PUBLIC_BLOGS": "api/blogs/public-blogs/",
        "EXPO_PUBLIC_FIREBASE_API_KEY": "$EXPO_PUBLIC_FIREBASE_API_KEY",
        "EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN": "$EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN",
        "EXPO_PUBLIC_FIREBASE_PROJECT_ID": "$EXPO_PUBLIC_FIREBASE_PROJECT_ID",
        "EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET": "$EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET",
        "EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "$EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID",
        "EXPO_PUBLIC_FIREBASE_APP_ID": "$EXPO_PUBLIC_FIREBASE_APP_ID",
        "EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID": "$EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID",
        "EXPO_PUBLIC_ANDROID_CLIENT_ID": "$EXPO_PUBLIC_ANDROID_CLIENT_ID",
        "EXPO_PUBLIC_IOS_CLIENT_ID": "$EXPO_PUBLIC_IOS_CLIENT_ID",
        "EXPO_PUBLIC_WEB_CLIENT_ID": "$EXPO_PUBLIC_WEB_CLIENT_ID",
        "EXPO_PUBLIC_EXPO_CLIENT_ID": "$EXPO_PUBLIC_EXPO_CLIENT_ID"
      }
    },
    "preview": {
      "distribution": "internal",
      "env": {
        "EXPO_PUBLIC_WEBSITE_DOMAIN": "$EAS_DEV_WEBSITE_DOMAIN",
        "EXPO_PUBLIC_BASE_URL": "$EAS_DEV_BASE_URL",
        "EXPO_PUBLIC_BLOGS": "api/blogs/public-blogs/",
        "EXPO_PUBLIC_FIREBASE_API_KEY": "$EXPO_PUBLIC_FIREBASE_API_KEY",
        "EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN": "$EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN",
        "EXPO_PUBLIC_FIREBASE_PROJECT_ID": "$EXPO_PUBLIC_FIREBASE_PROJECT_ID",
        "EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET": "$EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET",
        "EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "$EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID",
        "EXPO_PUBLIC_FIREBASE_APP_ID": "$EXPO_PUBLIC_FIREBASE_APP_ID",
        "EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID": "$EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID",
        "EXPO_PUBLIC_ANDROID_CLIENT_ID": "$EXPO_PUBLIC_ANDROID_CLIENT_ID",
        "EXPO_PUBLIC_IOS_CLIENT_ID": "$EXPO_PUBLIC_IOS_CLIENT_ID",
        "EXPO_PUBLIC_WEB_CLIENT_ID": "$EXPO_PUBLIC_WEB_CLIENT_ID",
        "EXPO_PUBLIC_EXPO_CLIENT_ID": "$EXPO_PUBLIC_EXPO_CLIENT_ID"
      }
    },
    "production": {
      "autoIncrement": true,
      "env": {
        "EXPO_PUBLIC_WEBSITE_DOMAIN": "$EAS_PROD_WEBSITE_DOMAIN",
        "EXPO_PUBLIC_BASE_URL": "$EAS_PROD_BASE_URL",
        "EXPO_PUBLIC_BLOGS": "api/blogs/public-blogs/",
        "EXPO_PUBLIC_FIREBASE_API_KEY": "$EXPO_PUBLIC_FIREBASE_API_KEY",
        "EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN": "$EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN",
        "EXPO_PUBLIC_FIREBASE_PROJECT_ID": "$EXPO_PUBLIC_FIREBASE_PROJECT_ID",
        "EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET": "$EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET",
        "EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "$EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID",
        "EXPO_PUBLIC_FIREBASE_APP_ID": "$EXPO_PUBLIC_FIREBASE_APP_ID",
        "EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID": "$EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID",
        "EXPO_PUBLIC_ANDROID_CLIENT_ID": "$EXPO_PUBLIC_ANDROID_CLIENT_ID",
        "EXPO_PUBLIC_IOS_CLIENT_ID": "$EXPO_PUBLIC_IOS_CLIENT_ID",
        "EXPO_PUBLIC_WEB_CLIENT_ID": "$EXPO_PUBLIC_WEB_CLIENT_ID",
        "EXPO_PUBLIC_EXPO_CLIENT_ID": "$EXPO_PUBLIC_EXPO_CLIENT_ID"
      }
    },
    "production-apk": {
      "autoIncrement": true,
      "android": {
        "buildType": "apk"
      },
      "env": {
        "EXPO_PUBLIC_WEBSITE_DOMAIN": "$EAS_PROD_WEBSITE_DOMAIN",
        "EXPO_PUBLIC_BASE_URL": "$EAS_PROD_BASE_URL",
        "EXPO_PUBLIC_BLOGS": "api/blogs/public-blogs/",
        "EXPO_PUBLIC_FIREBASE_API_KEY": "$EXPO_PUBLIC_FIREBASE_API_KEY",
        "EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN": "$EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN",
        "EXPO_PUBLIC_FIREBASE_PROJECT_ID": "$EXPO_PUBLIC_FIREBASE_PROJECT_ID",
        "EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET": "$EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET",
        "EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": "$EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID",
        "EXPO_PUBLIC_FIREBASE_APP_ID": "$EXPO_PUBLIC_FIREBASE_APP_ID",
        "EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID": "$EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID",
        "EXPO_PUBLIC_ANDROID_CLIENT_ID": "$EXPO_PUBLIC_ANDROID_CLIENT_ID",
        "EXPO_PUBLIC_IOS_CLIENT_ID": "$EXPO_PUBLIC_IOS_CLIENT_ID",
        "EXPO_PUBLIC_WEB_CLIENT_ID": "$EXPO_PUBLIC_WEB_CLIENT_ID",
        "EXPO_PUBLIC_EXPO_CLIENT_ID": "$EXPO_PUBLIC_EXPO_CLIENT_ID"
      }
    }
  },
  "submit": {
    "production": {}
  }
}
